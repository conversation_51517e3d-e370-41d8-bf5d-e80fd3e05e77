import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { SayTool, TextBlockParamVersion1 } from '../../types/type';
import { LangfuseGenerationClient } from 'langfuse';
import { PHASE_LIMIT_VALUES, UPDATE_PHASE_TOOL_NAME } from '@/agent/constants/tool';

/**
 * 更新阶段工具处理器
 */
export class UpdatePhaseHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const curPhase: string | undefined = block.params.current_phase;
    const phase: string | undefined = block.params.next_phase;
    const sharedMessageProps: SayTool = {
      tool: 'updatePhase',
      phase,
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!phase) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('update_phase', 'content', this.context),
            this.context.stateManager
          );
          return;
        }
        if(!PHASE_LIMIT_VALUES.includes(phase)) {
            this.context.stateManager.updateState({
              consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
            });
            ToolHelpers.pushToolResult(
              block,
              userMessageContent,
              'The phase value is not valid. the phase must be one of the following values: \n' + PHASE_LIMIT_VALUES.join(', '),
              this.context.stateManager
            );
            return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: UPDATE_PHASE_TOOL_NAME
        });
        await this.context.messageService.say('tool', JSON.stringify(sharedMessageProps), false);

        const toolLog = ToolHelpers.generateToolLog(UPDATE_PHASE_TOOL_NAME, this.context.loggerManager);
        toolLog.start(`${phase}`);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: phase
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        toolLog.end('success');
        generationCall?.end({

        });
        // const completeMessage = JSON.stringify({
        //   ...sharedMessageProps,
        //   content: results
        // } satisfies SayTool);
        // await this.context.messageService.say('tool', completeMessage, false);
        ToolHelpers.pushToolResult(block, userMessageContent, "The phase has been updated to " + phase + " successfully", this.context.stateManager);
        // this.context.loggerManager.perf({
        //   namespace: ASSISTANT_NAMESPACE,
        //   subtag: 'kwaipilot-ide-agent-chat-tool',
        //   millis: Date.now() - startToolTime,
        //   extra4: 'success',
        //   extra6: block.name
        // });
        // ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
        //   path: absolutePath,
        //   regex,
        //   filePattern
        // });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'update phase',
        error,
        UPDATE_PHASE_TOOL_NAME
      );
    }
  }
} 